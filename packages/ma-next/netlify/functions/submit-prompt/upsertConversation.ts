import { debug } from '../_shared/debug';
import { User } from '@supabase/supabase-js';
import { initServiceRoleSupabase, initUserRoleSupabase } from '../_shared/supabase';
import { SupabaseFacade } from '../_shared/supabaseFacade';
import { Message } from '../_protocol';
import { PromptRequest } from './types';


/**
 * Initialises or updates the conversation based on the submitted prompt,
 * always resepecting the conversationId generated by the frontend.
 */
export async function upsertConversation({
  supabase,
  supabaseUser,
  user,
  requestJSON: { id, message, isNew, mode },
}: {
  supabase: ReturnType<typeof initServiceRoleSupabase>;
  supabaseUser: ReturnType<typeof initUserRoleSupabase>;
  user: User;
  requestJSON: PromptRequest;
}) {
  const userProfilePromise = getUserProfilePromise(supabaseUser, user);
  const facade = new SupabaseFacade(supabaseUser);

  if (isNew) {
    debug('Creating new conversation');
    const { error } = await facade.createConversation({
      id,
      userId: user.id,
      title: message.content.substring(0, 50),
      messages: [message],
      mode,
    });

    if (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }

    return { messages: [message], oaiResponseId: undefined, userProfile: await userProfilePromise };
  }

  const { data, error } = await facade.appendConversationMessage(
    id,
    message ? message : null
  );

  if (error || !data) {
    console.error('Error updating conversation messages:', error);
    throw error || new Error('Failed to update conversation');
  }

  return {
    messages: data.messages as Message[],
    oaiResponseId: data.oaiResponseId,
    userProfile: await userProfilePromise,
  };
}

function getUserProfilePromise(supabaseUser: ReturnType<typeof initUserRoleSupabase>, user: User) {
  return supabaseUser
    .from('profiles')
    .select('firstName, lastName, preferences')
    .eq('id', user.id)
    .single()
    .then(({ data, error }) => {
      if (error || !data) {
        return undefined;
      }
      return data;
    });
}
