import { handleCors } from '../_shared/cors';
import { validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { User } from '@supabase/supabase-js';
import { initServiceRoleSupabase, initUserRoleSupabase } from '../_shared/supabase';
import { debug } from '../_shared/debug';
import { PromptRequest } from './types';

/**
 * Validates the request and authenticates the user
 */
async function validateAndAuthenticate(
  req: Request
): Promise<
  | [Response]
  | [
      null,
      User,
      ReturnType<typeof initServiceRoleSupabase>,
      ReturnType<typeof initUserRoleSupabase>,
      PromptRequest,
    ]
> {
  const corsResponse = handleCors(req);
  if (corsResponse) {
    debug('CORS response:', corsResponse);
    return [corsResponse];
  }

  if (req.method !== 'POST') {
    debug('Method not allowed:', req.method);
    return [validationError('Method not allowed')];
  }

  const requestJSON: PromptRequest = await req.json();
  const { id, message, mode = 'agent', confirmedTools, toolResults, isNew = false } = requestJSON;

  debug('Prompt:', message);
  debug('Client conversation ID:', id);
  debug('Mode:', mode);
  debug('Is new conversation:', isNew);
  if (toolResults) debug('Tool results:', toolResults);

  if (!id || !(message || confirmedTools || toolResults) || !mode) {
    debug('Conversation ID, message/confirmedTools/toolResults, and mode are required');
    return [
      validationError('Conversation ID, message/confirmedTools/toolResults, and mode are required'),
    ];
  }

  const auth = await initAuthenticate(req);

  if (auth[0]) debug('Authentication error:', auth[0]);
  if (auth[0]) return [auth[0]];

  return [...auth, requestJSON];
}

export { validateAndAuthenticate };
