import { initServiceRoleSupabase } from '../_shared/supabase';
import { SupabaseFacade } from '../_shared/supabaseFacade';
import { Message, MessagePart } from '../_protocol';

/**
 * Saves the Message[] streamed to the frontend to the conversation in the db
 */
export async function onFinish(
  parts: MessagePart[],
  priorMessages: Message[],
  supabase: ReturnType<typeof initServiceRoleSupabase>,
  id: string,
  isContinuation: boolean,
  mode: 'agent' | 'task',
  oaiResponseId: string,
  metrics: import('./types').PromptMetrics
) {
  const [messages, messageToUpdate] = isContinuation
    ? [priorMessages.slice(0, -1), priorMessages[priorMessages.length - 1]]
    : [priorMessages, null];

  try {
    const assistantMessage = (
      messageToUpdate
        ? {
            ...messageToUpdate,
            parts,
          }
        : {
            id: crypto.randomUUID(),
            role: 'assistant',
            parts,
            createdAt: new Date(),
          }
    ) as Message;

    // Save the Message[] streamed to the frontend to the conversation in the db
    const updated = [...messages];
    let idx = -1;
    for (let i = updated.length - 1; i >= 0; i--) {
      if (updated[i].role === 'user') {
        idx = i;
        break;
      }
    }
    if (idx !== -1) {
      updated[idx] = { ...(updated[idx] as Message), metrics };
    }

    const updateData: any = {
      messages: [...updated, assistantMessage],
      oaiResponseId,
    };

    // If mode is provided, update it as well
    if (mode) {
      updateData.mode = mode;
    }

    const facade = new SupabaseFacade(supabase);
    const { error: updateError } = await facade.updateConversation(id, updateData);

    if (updateError) {
      console.error('Error updating conversation messages:', updateError);
    }
  } catch (error) {
    console.error('Error saving message to database:', error);
  }
}
