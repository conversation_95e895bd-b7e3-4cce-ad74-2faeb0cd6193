import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { SupabaseFacade } from '../_shared/supabaseFacade';

function stripMetrics(messages: any[]) {
  return messages.map(m => {
    const { metrics, ...rest } = m as any;
    return rest;
  });
}

export default async function handler(req: Request) {
  const cors = handleCors(req);
  if (cors) return cors;

  if (req.method !== 'GET') {
    return validationError('Method not allowed');
  }

  const url = new URL(req.url);
  const id = url.searchParams.get('id');
  if (!id) return validationError('Conversation ID is required');

  const [authError, user] = await initAuthenticate(req);
  if (authError) return authError;

  const supabase = initServiceRoleSupabase();
  const facade = new SupabaseFacade(supabase);

  const { data, error } = await facade.getConversation(id);
  if (error || !data || data.userId !== user.id) {
    return errorResponse(error || 'Conversation not found');
  }

  const messages = Array.isArray(data.messages)
    ? stripMetrics(data.messages)
    : [];

  return new Response(
    JSON.stringify({
      id: data.id,
      title: data.title,
      messages,
      currentTaskflowId: data.currentTaskflowId,
      mode: data.mode,
      oaiResponseId: data.oaiResponseId,
    }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}
