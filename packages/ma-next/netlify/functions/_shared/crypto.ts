import { createCipher<PERSON>, createDecipher<PERSON>, pbkdf2Sync, randomBytes } from 'crypto';

/**
 * Encrypts data using AES-256-GCM, a symmetric cipher combining confidentiality and integrity.
 * AES-256 uses a 256-bit key, considered secure against brute-force attacks with current
 * technology (quantum-resistant for now). The Galois/Counter Mode (GCM) adds authentication,
 * ensuring data isn't tampered with. A 16-byte salt is generated to make key derivation unique
 * per encryption, preventing rainbow table attacks when using PBKDF2. A 12-byte
 * initialization vector (IV) ensures the same plaintext encrypts differently each time,
 * critical for semantic security. The key is derived from a secret using PBKDF2 with 100,000
 * iterations to slow down brute-force attempts. Best practice: use 256-bit keys for long-term
 * security; shorter keys (e.g., 128-bit) are faster but riskier with advancing compute power.
 * The version prefix supports key rotation, allowing new keys without breaking old data.
 */
async function encryptData(data: string, secret: string): Promise<string> {
  const { version, key: secretKey } = getSecretsAndVersion(secret);
  const salt = randomBytes(16);
  const key = getKey(secretKey, salt);
  const iv = randomBytes(12);
  const cipher = createCipheriv('aes-256-gcm', key, iv);
  const encrypted = Buffer.concat([cipher.update(data, 'utf8'), cipher.final()]);
  const tag = cipher.getAuthTag();
  const combined = Buffer.concat([salt, iv, tag, encrypted]);
  return `v${version}:${base64urlEncode(combined)}`;
}

/**
 * Decrypts AES-256-GCM encrypted data, verifying the authentication tag to detect tampering.
 * Extracts the salt, IV, and tag from the encoded payload, using the version to select the
 * correct secret. Throws errors for invalid data or unknown versions to prevent misuse, such as
 * probing attacks. The strict length check (44 bytes minimum) ensures the payload contains all
 * required components, avoiding undefined behavior in the cipher.
 */
async function decryptData(encrypted: string, secret: string): Promise<string> {
  let payload = encrypted;
  let version = '0';
  const match = /^v(\d+):(.+)$/.exec(encrypted);
  if (match) {
    version = match[1];
    payload = match[2];
  }

  const secrets = parseSecrets(secret);
  const secretKey = secrets[version];
  if (!secretKey) {
    throw new Error(`Unknown encryption key version: ${version}`);
  }

  const combined = base64urlDecode(payload);

  if (combined.length < 44) {
    throw new Error('Invalid encrypted data.');
  }
  const salt = combined.subarray(0, 16);
  const iv = combined.subarray(16, 28);
  const tag = combined.subarray(28, 44);
  const data = combined.subarray(44);
  const key = getKey(secretKey, salt);
  const decipher = createDecipheriv('aes-256-gcm', key, iv);
  decipher.setAuthTag(tag);
  const decrypted = Buffer.concat([decipher.update(data), decipher.final()]);
  return decrypted.toString('utf8');
}

/**
 * Conditionally encrypts JSON-serialized data, useful in systems where encryption is optional
 * (e.g., dev vs. prod environments or regulatory requirements). JSON serialization limits
 * usage to JSON-compatible data but simplifies handling complex objects. If no secret is
 * provided, data passes through unencrypted, which is practical for testing but risky if
 * misconfigured in production. Best practice: ensure secrets are set in sensitive environments
 * and use environment variables to avoid hardcoding.
 */
async function maybeEncrypt<T>(
  data: T,
  secret = process.env.DB_ENCRYPTION_KEYS
): Promise<string | T> {
  if (!secret) return data;
  return encryptData(JSON.stringify(data), secret);
}

/**
 * Conditionally decrypts data, assuming it was JSON-serialized and encrypted by maybeEncrypt.
 * Returns unchanged data if no secret is provided or data is null, supporting mixed workflows
 * where encrypted and plaintext data coexist. This flexibility is common in systems
 * transitioning to encryption or handling legacy data. Best practice: log decryption failures
 * for monitoring, as they may indicate misconfiguration or attacks.
 */
async function maybeDecrypt<T>(
  data: any,
  secret = process.env.DB_ENCRYPTION_KEYS
): Promise<T | any> {
  if (!secret || data == null) return data;
  const decrypted = await decryptData(data as string, secret);
  return JSON.parse(decrypted) as T;
}

export { encryptData, decryptData, maybeEncrypt, maybeDecrypt };

/**
 * Parses a secret, supporting both plain strings and JSON objects for versioned secrets.
 * Versioned secrets (e.g., { "0": "key1", "1": "key2" }) enable key rotation, a critical
 * practice for limiting the impact of key compromise. If parsing fails, falls back to
 * treating the input as a single secret, ensuring compatibility with simpler setups. Best
 * practice: use JSON for secrets in production to support rotation, and validate JSON
 * structure to avoid runtime errors.
 */
function parseSecrets(secret: string): Record<string, string> {
  if (secret.trim().startsWith('{')) {
    try {
      const parsed = JSON.parse(secret) as Record<string, string>;
      if (parsed && typeof parsed === 'object') {
        return parsed;
      }
    } catch {
      /* ignore */
    }
  }
  return { '0': secret };
}

/**
 * Selects the highest version from a secrets record, enabling the use of the latest key for
 * encryption. Sorting ensures deterministic selection, critical for consistent behavior across
 * systems. Defaults to '0' for plain secrets, maintaining compatibility. Key rotation is a
 * security must-have: new keys should increment versions numerically to avoid ambiguity.
 */
function getNewestVersion(secrets: Record<string, string>): string {
  return Object.keys(secrets).sort().at(-1) || '0';
}

/**
 * Derives a 256-bit key using PBKDF2, a slow key derivation function designed to resist
 * brute-force attacks. The salt ensures unique keys even if the same secret is reused,
 * thwarting precomputed attacks. The 100,000 iterations balance security and performance;
 * higher values increase resistance but slow down encryption. Best practice: use at least
 * 100,000 iterations and store salts with the ciphertext, never separately. The first 32
 * bytes of the secret are used to limit input size, avoiding performance issues with overly
 * long secrets. Note the two 32's here are unrelated: one is the output key size, the other is
 * the maximum secret size.
 */
function getKey(secret: string, salt: Buffer): Buffer {
  return pbkdf2Sync(secret.slice(0, 32), salt, 100000, 32, 'sha256');
}

/**
 * Encodes a buffer to base64url, a URL-safe variant of base64 that replaces '+' with '-' and
 * '/' with '_' to avoid URL encoding issues. Strips trailing '=' for compactness, which is
 * safe as the decoder can infer padding. Base64url is widely used in JWTs and other web
 * contexts for its compatibility with HTTP and JSON.
 */
function base64urlEncode(buffer: Buffer): string {
  return buffer.toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
}

/**
 * Decodes a base64url string back to a buffer, restoring '+' and '/' and adding padding as
 * needed. The padding restoration (adding '=') ensures compatibility with standard base64
 * decoders. This step is critical for correctly interpreting encoded ciphertexts, as errors
 * here could lead to invalid decryption inputs.
 */
function base64urlDecode(str: string): Buffer {
  str = str.replace(/-/g, '+').replace(/_/g, '/');
  while (str.length % 4) str += '=';
  return Buffer.from(str, 'base64');
}

/**
 * Extracts the version and corresponding secret key, using the newest version for encryption.
 * This abstraction simplifies key rotation logic, ensuring the latest key is used while
 * preserving access to older versions for decryption. Best practice: maintain a secure
 * key management system (e.g., AWS KMS or HashiCorp Vault) to store and rotate secrets,
 * and audit version usage to detect outdated keys.
 */
function getSecretsAndVersion(secret: string): { version: string; key: string } {
  const secrets = parseSecrets(secret);
  const version = getNewestVersion(secrets);
  const key = secrets[version];
  return { version, key };
}
