import { deepStrictEqual as assertEquals, rejects } from 'node:assert';
import { test } from 'node:test';
import {
  encryptData,
  decryptData,
  maybeEncrypt,
  maybeDecrypt,
} from './crypto';

const secret = 'a-very-secure-secret-key-that-is-long-enough';


test('encryptData and decryptData round trip', async () => {
  const original = 'my secret data';
  const encrypted = await encryptData(original, secret);
  const decrypted = await decryptData(encrypted, secret);
  assertEquals(decrypted, original);
});

test('decryptData throws with wrong secret', async () => {
  const original = 'data';
  const encrypted = await encryptData(original, secret);
  await rejects(() => decryptData(encrypted, 'another-secret'));
});

test('maybeEncrypt and maybeDecrypt round trip', async () => {
  const obj = { foo: 'bar' };
  const encrypted = await maybeEncrypt(obj, secret);
  const decrypted = await maybeDecrypt(encrypted, secret);
  assertEquals(decrypted, obj);
});

test('encryptData uses highest version', async () => {
  const secrets = JSON.stringify({ '0': 'old', '1': secret });
  const encrypted = await encryptData('vtest', secrets);
  const decrypted = await decryptData(encrypted, secrets);
  assertEquals(decrypted, 'vtest');
});

test('maybeEncrypt returns original when no secret', async () => {
  const str = 'noop';
  const encrypted = await maybeEncrypt(str, undefined);
  assertEquals(encrypted, str);
  const decrypted = await maybeDecrypt(encrypted, undefined);
  assertEquals(decrypted, str);
});
