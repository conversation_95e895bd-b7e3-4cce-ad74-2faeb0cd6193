import { SupabaseClient } from '@supabase/supabase-js';
import { maybeEncrypt, maybeDecrypt } from './crypto';
import type { Message } from '../_protocol';

class SupabaseFacade {
  private client: SupabaseClient;
  constructor(client: SupabaseClient) {
    this.client = client;
  }

  private async maybeEncrypt(data: any) {
    return maybeEncrypt(data);
  }

  private async maybeDecrypt(data: any) {
    return maybeDecrypt(data);
  }

  async getConversation(id: string) {
    const res = await this.client
      .from('conversations')
      .select('id, title, messages, currentTaskflowId, mode, oaiResponseId, userId')
      .eq('id', id)
      .single();
    if (res.data) {
      res.data.title = await this.maybeDecrypt(res.data.title);
      res.data.messages = await this.maybeDecrypt(res.data.messages);
    }
    return res;
  }

  async listConversations(userId: string) {
    const res = await this.client
      .from('conversations')
      .select(
        `
        id,
        title,
        createdAt,
        updatedAt,
        userId,
        currentTaskflowId,
        mode,
        agent:taskflows!conversations_currentTaskflowId_fkey(
          id,
          active,
          triggers: sync_triggers (
            providerKey,
            syncScope
          )
        )
      `
      )
      .eq('userId', userId)
      .order('updatedAt', { ascending: false });

    if (res.data) {
      for (const conv of res.data) {
        conv.title = await this.maybeDecrypt(conv.title);
      }
    }
    return res;
  }

  async createConversation(params: {
    id: string;
    userId: string;
    title: string | null;
    messages: Message[];
    mode: 'agent' | 'task';
  }) {
    const encryptedTitle = params.title !== null ? await this.maybeEncrypt(params.title) : null;
    const encryptedMessages = await this.maybeEncrypt(params.messages);
    return this.client
      .from('conversations')
      .insert({
        id: params.id,
        title: encryptedTitle,
        userId: params.userId,
        messages: encryptedMessages,
        mode: params.mode,
      })
      .select()
      .single();
  }

  async appendConversationMessage(id: string, message: Message | null) {
    const { data, error } = await this.client
      .from('conversations')
      .select('messages, oaiResponseId')
      .eq('id', id)
      .single();
    if (error || !data) return { data: null, error } as const;
    const decrypted: Message[] = (await this.maybeDecrypt(data.messages)) || [];
    if (message) decrypted.push(message);
    const encrypted = await this.maybeEncrypt(decrypted);
    const { data: updated, error: updateError } = await this.client
      .from('conversations')
      .update({ messages: encrypted })
      .eq('id', id)
      .select('messages, oaiResponseId')
      .single();
    if (updated) {
      updated.messages = await this.maybeDecrypt(updated.messages);
    }
    return { data: updated, error: updateError } as const;
  }

  async updateConversation(id: string, updates: Record<string, any>) {
    const processed: Record<string, any> = { ...updates };
    if (processed.title !== undefined) {
      processed.title = await this.maybeEncrypt(processed.title);
    }
    if (processed.messages !== undefined) {
      processed.messages = await this.maybeEncrypt(processed.messages);
    }
    return this.client.from('conversations').update(processed).eq('id', id);
  }

  async deleteConversation(id: string) {
    return this.client.from('conversations').delete().eq('id', id);
  }
}

export { SupabaseFacade };
