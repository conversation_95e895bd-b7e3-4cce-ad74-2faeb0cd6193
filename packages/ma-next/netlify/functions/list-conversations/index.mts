import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { SupabaseFacade } from '../_shared/supabaseFacade';

export default async function handler(req: Request) {
  const cors = handleCors(req);
  if (cors) return cors;

  if (req.method !== 'GET') {
    return validationError('Method not allowed');
  }

  const [authError, user] = await initAuthenticate(req);
  if (authError) return authError;

  const supabase = initServiceRoleSupabase();
  const facade = new SupabaseFacade(supabase);

  const { data, error } = await facade.listConversations(user.id);
  if (error) return errorResponse(error);

  return new Response(JSON.stringify(data), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  });
}
