import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { SupabaseFacade } from '../_shared/supabaseFacade';

export default async function handler(req: Request) {
  const cors = handleCors(req);
  if (cors) return cors;

  if (req.method !== 'POST') {
    return validationError('Method not allowed');
  }

  try {
    const [authError, user] = await initAuthenticate(req);
    if (authError) return authError;

    const { id, updates } = await req.json();
    if (!id || !updates) {
      return validationError('id and updates are required');
    }

    const supabase = initServiceRoleSupabase();
    const facade = new SupabaseFacade(supabase);

    const { data, error } = await facade.getConversation(id);
    if (error || !data || data.userId !== user.id) {
      return errorResponse(error || 'Conversation not found');
    }

    const { error: updateError } = await facade.updateConversation(id, updates);
    if (updateError) return errorResponse(updateError);

    return new Response(JSON.stringify({ success: true }), { headers: corsHeaders });
  } catch (err) {
    return errorResponse(err);
  }
}
