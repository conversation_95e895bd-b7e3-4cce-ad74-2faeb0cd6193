import { createStore } from './createStore';
import { supabase } from '../lib/supabase';
import { fetchConversations } from '../lib/conversation';
import { Conversation } from '../types';

export interface AgentState {
  agents: Conversation[];
  isLoading: boolean;
}

const initialState: AgentState = {
  agents: [],
  isLoading: false,
};

/**
 * Aka ConversationsStore (note plural), because of how Agents & Conversations are sometimes interchangeable in the UI.
 */
class AgentsStore {
  private static instance: AgentsStore;
  private store;
  private subscriptions: any[] = [];

  private constructor() {
    this.store = createStore<AgentState>(initialState);
    this.init();
  }

  private async init() {
    // Initialize real-time subscription when auth state changes
    supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        setTimeout(() => {
          if (!this.store.getState().agents.length) {
            this.loadAgents();
            this.setupSubscriptions();
          }
        }, 100);
      } else if (event === 'SIGNED_OUT') {
        this.cleanup();
      }
    });
  }

  static getInstance(): AgentsStore {
    if (!AgentsStore.instance) {
      AgentsStore.instance = new AgentsStore();
    }
    return AgentsStore.instance;
  }

  async loadAgents() {
    try {
      // Only set loading on initial load
      if (this.store.getState().agents.length === 0) {
        this.store.setState({ isLoading: true });
      }

      // Get all conversations with their current agent (if any)
      const conversations = await fetchConversations();

      this.store.setState({
        agents: conversations || [],
        isLoading: false,
      });
    } catch (error) {
      console.error('Failed to load agents:', error);
      console.log('Error details:', JSON.stringify(error, null, 2));
      this.store.setState({ isLoading: false });
    }
  }

  private setupSubscriptions() {
    // Clean up any existing subscriptions
    this.cleanupSubscriptions();

    // Subscribe to changes in the conversations table
    const conversationsSubscription = supabase
      .channel('conversations-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'conversations',
        },
        () => {
          this.loadAgents();
        }
      )
      .subscribe();

    // Subscribe to changes in the taskflows table
    const taskflowsSubscription = supabase
      .channel('taskflows-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'taskflows',
        },
        () => {
          console.log('Conversations table changed');
          this.loadAgents();
        }
      )
      .subscribe();

    // Store subscriptions for cleanup
    this.subscriptions = [conversationsSubscription, taskflowsSubscription];
  }

  private cleanupSubscriptions() {
    this.subscriptions.forEach(subscription => {
      if (subscription) {
        subscription.unsubscribe();
      }
    });
    this.subscriptions = [];
  }

  private cleanup() {
    this.cleanupSubscriptions();
    this.store.setState(initialState);
  }

  async toggleAgent(agentId: string) {
    const { agents } = this.store.getState();
    const agent = agents.find(a => a.agent?.id === agentId)?.agent;
    if (!agent) return;

    try {
      const { error } = await supabase
        .from('taskflows')
        .update({ active: !agent.active })
        .eq('id', agentId);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to toggle agent:', error);
    }
  }

  useAgentsState() {
    return this.store.useStoreState();
  }

  useAgentStatus(agentId: string | null) {
    return this.store.useStoreState(state => {
      if (!agentId) return false;
      const agent = state.agents.find(a => a.agent?.id === agentId)?.agent;
      return agent?.active || false;
    });
  }
}

function useAgentsStore() {
  return [agentsStore.useAgentsState(), agentsStore] as const;
}

export { useAgentsStore };
const agentsStore = AgentsStore.getInstance();
export { agentsStore };
