import { invokeFunction } from '../utils/invokeFunction';

async function fetchConversation(id: string) {
  const { data, error } = await invokeFunction(`get-conversation?id=${id}`, {
    method: 'GET',
  });
  if (error) {
    console.error('Error fetching conversation:', error);
    throw error;
  }
  return data as any;
}

async function fetchConversations() {
  const { data, error } = await invokeFunction('list-conversations', {
    method: 'GET',
  });
  if (error) {
    console.error('Error fetching conversations:', error);
    throw error;
  }
  return data as any[];
}

async function updateConversation(id: string, updates: Record<string, any>) {
  const { error } = await invokeFunction('update-conversation', {
    body: { id, updates },
  });
  if (error) {
    console.error('Error updating conversation:', error);
    throw error;
  }
}

async function deleteConversation(id: string) {
  const { error } = await invokeFunction('delete-conversation', {
    body: { id },
  });
  if (error) {
    console.error('Error deleting conversation:', error);
    throw error;
  }
}

export { fetchConversation, fetchConversations, updateConversation, deleteConversation };
