import { useCallback } from 'react';
import { fetchConversation } from 'lib/conversation';
import { Message } from './protocol';

type UseReloadProps = {
  id: string;
  isNew: boolean;
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  setStatus: React.Dispatch<React.SetStateAction<'ready' | 'streaming' | 'submitted' | 'error'>>;
  setError: React.Dispatch<React.SetStateAction<Error | null>>;
  setCurrentTaskflowId: React.Dispatch<React.SetStateAction<string | null>>;
  setMode: React.Dispatch<React.SetStateAction<'agent' | 'task'>>;
};

/**
 * Custom hook to handle reloading conversation messages
 */
function useReload({
  id,
  isNew,
  setMessages,
  setStatus,
  setError,
  setCurrentTaskflowId,
  setMode,
}: UseReloadProps) {
  const reload = useCallback(async () => {
    // If it's a new chat, just reset messages
    if (isNew) {
      setMessages([]);
      setCurrentTaskflowId(null); // Reset taskflow ID for new chats
      setStatus('ready'); // Ensure status is ready for new chats
      return;
    }

    try {
      setStatus('submitted');
      setError(null); // Clear previous errors

      const conversation = await fetchConversation(id);

      if (conversation?.messages) {
        const loadedMessages = conversation.messages.map((msg: any) => ({
          ...msg,
          createdAt: new Date(msg.createdAt),
        }));
        setMessages(loadedMessages);
      } else {
        // If no messages found, reset to empty state
        setMessages([]);
      }
      setCurrentTaskflowId(conversation.currentTaskflowId ?? null);

      // Set the mode if it exists in the conversation
      if (conversation.mode) {
        setMode(conversation.mode);
      }

      setStatus('ready');
    } catch (e) {
      console.error('Error reloading messages:', e);
      setError(e as Error);
      setStatus('error');
    }
  }, [id, isNew, setMessages, setStatus, setError, setCurrentTaskflowId, setMode]);

  return reload;
}

export { useReload };
