import React, { useRef, useEffect } from 'react';
import { Mail, Paperclip, Tag, Calendar, User } from 'lucide-react';
import { format } from 'date-fns';
import { GmailMessage, GmailAttachmentInfo } from 'src/config/nangoModels';

interface GmailSingleMessageDisplayProps {
  output: GmailMessage;
}

/**
 * Renders a rich display of a single Gmail message
 */
function GmailSingleMessageDisplay({ output }: GmailSingleMessageDisplayProps) {
  const message = output;
  const stripInlineAttachments = (html: string) =>
    html.replace(/<img[^>]*src=["']cid:[^"']+["'][^>]*>/gi, '');
  const bodyIframeRef = useRef<HTMLIFrameElement>(null);

  // Detect if the message body is HTML
  const sanitizedBody = stripInlineAttachments(message.body || '');
  const isHtmlEmail =
    sanitizedBody.includes('<') && (sanitizedBody.includes('</') || sanitizedBody.includes('/>'));

  useEffect(() => {
    // Only handle HTML emails in iframe
    if (isHtmlEmail && bodyIframeRef.current && sanitizedBody) {
      const iframeDocument = bodyIframeRef.current.contentDocument;
      if (iframeDocument) {
        iframeDocument.open();
        iframeDocument.write(`
          <html>
            <head>
              <style>
                body {
                  font-family: system-ui, -apple-system, sans-serif;
                  font-size: 14px;
                  color: ${window.matchMedia('(prefers-color-scheme: dark)').matches ? '#e5e7eb' : '#1f2937'};
                  margin: 0;
                  padding: 0;
                }
                a {
                  color: ${window.matchMedia('(prefers-color-scheme: dark)').matches ? '#60a5fa' : '#2563eb'};
                  text-decoration: none;
                }
                a:hover {
                  text-decoration: underline;
                }
              </style>
            </head>
            <body>${sanitizedBody}</body>
          </html>
        `);
        iframeDocument.close();
      }
    }
  }, [sanitizedBody, isHtmlEmail]);

  // Check if we have valid data
  if (!message) {
    return (
      <div className="p-6 text-center">
        <Mail className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No message data available</p>
      </div>
    );
  }

  // Extract headers if available
  const headers = message.headers || [];
  const getHeader = (name: string): string => {
    const header = headers.find(h => h.name.toLowerCase() === name.toLowerCase());
    return header ? header.value : '';
  };

  const subject = getHeader('subject') || 'No Subject';
  const from = getHeader('from') || '';
  const to = getHeader('to') || '';
  const date = getHeader('date') || message.internalDate || '';

  // Format date if available
  let formattedDate = '';
  if (date) {
    try {
      formattedDate = format(new Date(date), 'MMM d, yyyy h:mm a');
    } catch (e) {
      formattedDate = date;
    }
  }

  // Get attachments
  const attachments = message.attachments || [];

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Mail className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">Gmail Message</h3>
        </div>
      </div>

      <div className="p-5 space-y-4">
        {/* Email metadata */}
        <div className="space-y-2">
          {/* From */}
          {from && (
            <div className="flex items-start">
              <User className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <div className="text-xs font-medium text-gray-500 dark:text-gray-400">From:</div>
                <div className="text-sm text-gray-800 dark:text-gray-200">{from}</div>
              </div>
            </div>
          )}

          {/* To */}
          {to && (
            <div className="flex items-start">
              <User className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <div className="text-xs font-medium text-gray-500 dark:text-gray-400">To:</div>
                <div className="text-sm text-gray-800 dark:text-gray-200">{to}</div>
              </div>
            </div>
          )}

          {/* Date */}
          {formattedDate && (
            <div className="flex items-start">
              <Calendar className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Date:</div>
                <div className="text-sm text-gray-800 dark:text-gray-200">{formattedDate}</div>
              </div>
            </div>
          )}
        </div>

        {/* Labels */}
        {message.labelIds && message.labelIds.length > 0 && (
          <div className="flex items-start">
            <Tag className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
            <div>
              <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                Labels:
              </div>
              <div className="flex flex-wrap gap-1.5">
                {message.labelIds.map((label: string) => (
                  <span
                    key={label}
                    className="inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                  >
                    {label.replace('CATEGORY_', '').toLowerCase()}
                  </span>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Attachments */}
        {attachments.length > 0 && (
          <div className="flex items-start">
            <Paperclip className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
            <div>
              <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                Attachments ({attachments.length}):
              </div>
              <div className="space-y-1">
                {attachments.map((attachment: GmailAttachmentInfo, index: number) => (
                  <div
                    key={attachment.attachmentId || index}
                    className="text-sm text-gray-800 dark:text-gray-200 flex items-center"
                  >
                    <span className="truncate">{attachment.filename}</span>
                    <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                      ({Math.round(attachment.size / 1024)} KB)
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Message body */}
        {message.body && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="prose prose-sm max-w-none dark:prose-invert">
              {isHtmlEmail ? (
                <iframe
                  ref={bodyIframeRef}
                  title="Email content"
                  className="w-full"
                  style={{
                    border: 'none',
                    minHeight: '800px',
                    height: 'auto',
                    overflow: 'auto',
                  }}
                  sandbox="allow-same-origin"
                />
              ) : (
                <div className="text-sm text-gray-800 dark:text-gray-200">
                  {processPlainText(sanitizedBody)}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Snippet if no body */}
        {!message.body && message.snippet && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-800 dark:text-gray-200">{message.snippet}</div>
          </div>
        )}
      </div>
    </div>
  );
}

// Process plain text: split into paragraphs and convert URLs to links
const processPlainText = (text: string) => {
  // Split into paragraphs on double newlines
  const paragraphs = text.split('\n\n').filter(p => p.trim());

  // URL regex for detecting links
  const urlRegex = /(https?:\/\/[^\s<>"']+)/g;

  return paragraphs.map((paragraph, index) => {
    // Convert URLs to anchor tags
    const formattedText = paragraph.replace(
      urlRegex,
      url =>
        `<a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline">${url}</a>`
    );

    return (
      <p
        key={index}
        className="mb-4 text-sm text-gray-800 dark:text-gray-200"
        dangerouslySetInnerHTML={{ __html: formattedText }}
      />
    );
  });
};

export { GmailSingleMessageDisplay };
