import { useState } from 'react';
import { agentsStore } from 'stores/agents';
import { deleteConversation, updateConversation } from 'lib/conversation';
import { useModal } from 'hooks/useModal';
import { useChatContext } from 'providers/ChatContext';
import { useRouter } from 'next/navigation';
import { ROUTES } from 'src/config';

function useAgentManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [includeConversations, setIncludeConversations] = useState(false);
  const [showActiveOnly, setShowActiveOnly] = useState(false);
  const chat = useChatContext();
  const { agents, isLoading } = agentsStore.useAgentsState();
  const { closeModal } = useModal();
  const router = useRouter();

  const handleSelectConversation = async (id: string) => {
    try {
      // In ma-next, we navigate to the chat page instead of loading the conversation
      router.push(ROUTES.chat(id));
      closeModal();
    } catch (error) {
      console.error('Failed to load conversation:', error);
    }
  };

  const handleCreateNewConversation = async () => {
    try {
      // In ma-next, we navigate to the home page to create a new conversation
      router.push(ROUTES.home);
      closeModal();
    } catch (error) {
      console.error('Failed to create new conversation:', error);
    }
  };

  const handleDeleteConversation = async (id: string) => {
    try {
      await deleteConversation(id);

      // If we're deleting the current conversation, navigate to home
      if (chat.id === id) {
        router.push(ROUTES.home);
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error);
    }
  };

  const handleEditConversation = async (id: string, title: string) => {
    try {
      await updateConversation(id, { title });
    } catch (error) {
      console.error('Failed to update conversation title:', error);
    }
  };

  // Filter conversations based on search term, includeConversations flag, and active status
  const filteredConversations = agents.filter(conversation => {
    const matchesSearch = conversation.title?.toLowerCase().includes(searchTerm.toLowerCase());
    const hasAgent = conversation.currentTaskflowId !== null;
    const isActive = conversation.agent?.active;

    if (!includeConversations && !hasAgent) {
      return false;
    }

    if (showActiveOnly && (!hasAgent || !isActive)) {
      return false;
    }

    return matchesSearch;
  });

  return {
    conversations: filteredConversations,
    isLoading,
    searchTerm,
    setSearchTerm,
    includeConversations,
    setIncludeConversations,
    showActiveOnly,
    setShowActiveOnly,
    handleSelectConversation,
    handleCreateNewConversation,
    handleDeleteConversation,
    handleEditConversation,
  };
}

export { useAgentManagement };
