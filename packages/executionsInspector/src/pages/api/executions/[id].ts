import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { decryptData } from '@/lib/crypto';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  const { id } = req.query;
  if (typeof id !== 'string') {
    return res.status(400).json({ error: 'Invalid id' });
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  if (!supabaseUrl || !serviceKey) {
    return res.status(500).json({ error: 'Supabase environment variables missing' });
  }

  const supabase = createClient(supabaseUrl, serviceKey);
  const encryptionKey = process.env.DB_ENCRYPTION_KEYS;
  const decryptField = async (val: any) => {
    if (!encryptionKey || val == null) return val;
    try {
      const dec = await decryptData(val as string, encryptionKey);
      return JSON.parse(dec);
    } catch {
      return val;
    }
  };

  try {
    const { data: execution, error } = await supabase
      .from('taskflow_executions')
      .select('*')
      .eq('id', id)
      .maybeSingle();
    if (error) throw error;
    if (!execution) return res.status(404).json({ error: 'Not found' });

    execution.triggerData = await decryptField(execution.triggerData);
    execution.context = await decryptField(execution.context);
    execution.result = await decryptField(execution.result);

    const { data: trace } = await supabase
      .from('execution_traces')
      .select('*')
      .eq('executionId', id)
      .maybeSingle();
    if (trace) {
      trace.trace = await decryptField(trace.trace);
    }

    return res.status(200).json({ execution, trace: trace || null });
  } catch (err: any) {
    console.error('Error fetching execution', err);
    return res.status(500).json({ error: err.message || 'Internal error' });
  }
}
