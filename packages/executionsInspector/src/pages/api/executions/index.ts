import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { decryptData } from '@/lib/crypto';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  if (!supabaseUrl || !serviceKey) {
    return res.status(500).json({ error: 'Supabase environment variables missing' });
  }

  const supabase = createClient(supabaseUrl, serviceKey);
  const encryptionKey = process.env.DB_ENCRYPTION_KEYS;
  const decryptField = async (val: any) => {
    if (!encryptionKey || val == null) return val;
    try {
      const dec = await decryptData(val as string, encryptionKey);
      return JSON.parse(dec);
    } catch {
      return val;
    }
  };

  try {
    const { data, error } = await supabase
      .from('taskflow_executions')
      .select(`
        *,
        taskflows!taskflow_executions_taskflowId_fkey (
          *,
          conversations!taskflows_conversationId_fkey (
            *,
            profiles!conversations_userId_fkey (
              id, firstName, lastName, preferences, createdAt, updatedAt
            )
          )
        )
      `)
      .order('updatedAt', { ascending: false });

    if (error) throw error;

    if (data) {
      for (const row of data as any[]) {
        row.triggerData = await decryptField(row.triggerData);
        row.context = await decryptField(row.context);
        row.result = await decryptField(row.result);
      }
    }

    return res.status(200).json({ data });
  } catch (err: any) {
    console.error('Error fetching executions', err);
    return res.status(500).json({ error: err.message || 'Internal error' });
  }
}
