import { createCipheriv, createDecipheriv, pbkdf2Sync, randomBytes } from 'crypto';

function parseSecrets(secret: string): Record<string, string> {
  if (secret.trim().startsWith('{')) {
    try {
      const parsed = JSON.parse(secret) as Record<string, string>;
      if (parsed && typeof parsed === 'object') {
        return parsed;
      }
    } catch {
      /* ignore */
    }
  }
  return { '0': secret };
}

function getNewestVersion(secrets: Record<string, string>): string {
  return Object.keys(secrets).sort().at(-1) || '0';
}

function getKey(secret: string, salt: Buffer): Buffer {
  return pbkdf2Sync(secret.slice(0, 32), salt, 100000, 32, 'sha256');
}

function base64urlEncode(buffer: Buffer): string {
  return buffer
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');
}

function base64urlDecode(str: string): Buffer {
  str = str.replace(/-/g, '+').replace(/_/g, '/');
  while (str.length % 4) str += '=';
  return Buffer.from(str, 'base64');
}

function getSecretsAndVersion(secret: string): { version: string; key: string } {
  const secrets = parseSecrets(secret);
  const version = getNewestVersion(secrets);
  const key = secrets[version];
  return { version, key };
}

async function encryptData(data: string, secret: string): Promise<string> {
  const { version, key: secretKey } = getSecretsAndVersion(secret);
  const salt = randomBytes(16);
  const key = getKey(secretKey, salt);
  const iv = randomBytes(12);
  const cipher = createCipheriv('aes-256-gcm', key, iv);
  const encrypted = Buffer.concat([cipher.update(data, 'utf8'), cipher.final()]);
  const tag = cipher.getAuthTag();
  const combined = Buffer.concat([salt, iv, tag, encrypted]);
  return `v${version}:${base64urlEncode(combined)}`;
}

async function decryptData(encrypted: string, secret: string): Promise<string> {
  let payload = encrypted;
  let version = '0';
  const match = /^v(\d+):(.+)$/.exec(encrypted);
  if (match) {
    version = match[1];
    payload = match[2];
  }

  const secrets = parseSecrets(secret);
  const secretKey = secrets[version];
  if (!secretKey) {
    throw new Error(`Unknown encryption key version: ${version}`);
  }

  const combined = base64urlDecode(payload);

  if (combined.length < 44) {
    throw new Error('Invalid encrypted data.');
  }
  const salt = combined.slice(0, 16);
  const iv = combined.slice(16, 28);
  const tag = combined.slice(28, 44);
  const data = combined.slice(44);
  const key = getKey(secretKey, salt);
  const decipher = createDecipheriv('aes-256-gcm', key, iv);
  decipher.setAuthTag(tag);
  const decrypted = Buffer.concat([decipher.update(data), decipher.final()]);
  return decrypted.toString('utf8');
}

export { encryptData, decryptData };
